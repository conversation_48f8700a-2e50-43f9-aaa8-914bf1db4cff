"""Add selected_models to Persona

Revision ID: 47e5bef3a1d8
Revises: 3fa9727197b7
Create Date: 2024-03-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "47e5bef3a1d8"
down_revision = "3fa9727197b7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add selected_models column as a JSONB array of strings
    op.add_column(
        "persona",
        sa.Column("selected_models", postgresql.JSONB(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("persona", "selected_models") 