import React, { useState, useRef, useEffect } from "react";
import { getDisplayNameForModel } from "@/lib/hooks";
import { FullLLMProvider } from "@/app/admin/configuration/llm/interfaces";
import { getProviderIcon } from "@/app/admin/configuration/llm/interfaces";
import { Check } from "lucide-react";
import { ChevronDownIcon } from "@/components/icons/icons";

interface ModelMultiSelectorProps {
  llmProviders: FullLLMProvider[];
  selectedModels: string[];
  onSelect: (selectedModels: string[]) => void;
  requiresImageGeneration?: boolean;
}

export const ModelMultiSelector: React.FC<ModelMultiSelectorProps> = ({
  llmProviders,
  selectedModels,
  onSelect,
  requiresImageGeneration = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const seenModelNames = new Set<string>();

  // Get all available models from providers
  const modelOptions = llmProviders.flatMap((provider) => {
    return (provider.display_model_names || provider.model_names)
      .filter((modelName) => {
        const displayName = getDisplayNameForModel(modelName);
        if (seenModelNames.has(displayName)) {
          return false;
        }
        seenModelNames.add(displayName);
        return true;
      })
      .map((modelName) => ({
        name: getDisplayNameForModel(modelName),
        value: modelName,
        provider: provider.name,
        icon: getProviderIcon(provider.provider, modelName),
      }));
  });

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleModelToggle = (modelValue: string) => {
    const newSelectedModels = selectedModels.includes(modelValue)
      ? selectedModels.filter((m) => m !== modelValue)
      : [...selectedModels, modelValue];
    onSelect(newSelectedModels);
  };

  const selectedDisplayNames = selectedModels.map((model) => {
    const option = modelOptions.find((opt) => opt.value === model);
    return option ? option.name : model;
  });

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className="flex items-center justify-between w-full px-3 py-2 text-sm border rounded-md cursor-pointer bg-background hover:bg-accent-background"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex flex-wrap gap-1">
          {selectedDisplayNames.length > 0 ? (
            selectedDisplayNames.map((name) => (
              <span
                key={name}
                className="px-2 py-0.5 text-xs bg-accent-background rounded"
              >
                {name}
              </span>
            ))
          ) : (
            <span className="text-muted-foreground">Select models...</span>
          )}
        </div>
        <ChevronDownIcon className="w-4 h-4 ml-2" />
      </div>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-background border rounded-md shadow-lg max-h-96 overflow-y-auto">
          <div className="py-1">
            {modelOptions.map((option) => {
              const isSelected = selectedModels.includes(option.value);
              return (
                <div
                  key={option.value}
                  className="flex items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent-background"
                  onClick={() => handleModelToggle(option.value)}
                >
                  <div className="flex items-center w-full">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={() => {}}
                      className="mr-2"
                      onClick={(e) => e.stopPropagation()}
                    />
                    {option.icon && (
                      <span className="mr-2">
                        {option.icon({ size: 16 })}
                      </span>
                    )}
                    <div className="flex flex-col">
                      <span className="font-medium">{option.name}</span>
                      <span className="text-xs text-muted-foreground">
                        {option.provider}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}; 